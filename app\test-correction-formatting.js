/**
 * Test script to verify the formatting improvements in CorrectionTool
 */

import { CorrectionTool } from './2translate/tools/correction-tool.js';

// Test the formatting methods
async function testFormatting() {
  console.log('🧪 Testing CorrectionTool formatting improvements...\n');
  
  const correctionTool = new CorrectionTool();
  
  // Test 1: displayFormattedChangesSummary with JSON array
  console.log('📋 Test 1: Formatted changes summary (JSON array)');
  const jsonArrayChanges = JSON.stringify([
    {
      "line_number": 34,
      "original_line": "Dopóki ona lubi moją pracę, jest warto.",
      "improved_line": "Jeśli tylko ona docenia moją pracę, to wszystko jest tego warte.",
      "reason": "Improved naturalness and grammar"
    },
    {
      "line_number": 12,
      "original_line": "To wyjaśnia czemu jest takie dziewczęce.",
      "improved_line": "To wyjaśnia, czemu jest takie dziewczęce.",
      "reason": "Added missing comma before 'czemu'"
    }
  ]);
  correctionTool.displayFormattedChangesSummary(jsonArrayChanges);
  
  console.log('\n' + '='.repeat(60) + '\n');
  
  // Test 2: displayFormattedChangesSummary with malformed string
  console.log('📋 Test 2: Formatted changes summary (malformed string)');
  const malformedChanges = `{
    "line_number": 34,
    "original_line": "Dopóki ona lubi moją pracę, jest warto.",
    "improved_line": "Jeśli tylko ona docenia moją pracę, to wszystko jest tego warte.",
    "reason": "Improved naturalness and grammar"
  }`;
  correctionTool.displayFormattedChangesSummary(malformedChanges);
  
  console.log('\n' + '='.repeat(60) + '\n');
  
  // Test 3: displayImprovedTranslation with JSON array
  console.log('📝 Test 3: Improved translation display (JSON array)');
  const jsonTranslation = JSON.stringify([
    { "speaker": "sign", "translation": "Czy przyjaźń między chłopakiem a dziewczyną może przetrwać?" },
    { "speaker": "sign", "translation": "(Nie, nie może!!)" },
    { "speaker": "Sign", "translation": "Czekoladowy Kosmos Miłości" },
    { "speaker": "Sawamura", "translation": "Hej, Natsume! Co słychać?" },
    { "speaker": "Yu", "translation": "D-dzień dobry." }
  ]);
  correctionTool.displayImprovedTranslation(jsonTranslation);
  
  console.log('\n' + '='.repeat(60) + '\n');
  
  // Test 4: displayPlainTextTranslation
  console.log('📝 Test 4: Plain text translation display');
  const plainTextTranslation = `Sawamura: Hej, Natsume! Co słychać?
Yu: D-dzień dobry.
Sawamura: Niezłe, to nowe?
Yu: P-pożyczyłem od siostry.
Sawamura: Ekstra.`;
  correctionTool.displayPlainTextTranslation(plainTextTranslation);
  
  console.log('\n✅ All formatting tests completed!');
}

// Run the test
testFormatting().catch(console.error);
