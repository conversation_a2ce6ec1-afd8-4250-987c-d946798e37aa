// @ts-check

/**
 * Claude 4 Sonnet Translation Engine with Tool Use
 * 
 * This module provides an advanced translation system that utilizes Claude 4 Sonnet
 * with tool use capabilities for intelligent anime subtitle translation.
 * 
 * Features:
 * - Intelligent scene detection for dynamic chunking
 * - Screenshot analysis for visual context
 * - Iterative correction and improvement
 * - Context-aware translation with character consistency
 * - Polish language optimization
 */

import Anthropic from '@anthropic-ai/sdk';
import { SceneDetector } from './tools/scene-detector.js';
import { ScreenshotTool } from './tools/screenshot-tool.js';
import { CorrectionTool } from './tools/correction-tool.js';
import { SecondLanguageValidator } from './tools/second-language-validator.js';
import { ExamplesReference } from './tools/examples-reference.js';
import { MetadataPersistence } from './tools/metadata-persistence.js';
import { ContextManager } from './context-manager.js';
import { getValidatedConfig } from './config.js';
import fs from 'fs';
import path from 'path';

// Color constants for better log readability
const COLORS = {
  RESET: '\x1b[0m',
  BRIGHT: '\x1b[1m',
  DIM: '\x1b[2m',

  // Text colors
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  GRAY: '\x1b[90m',

  // Background colors
  BG_RED: '\x1b[41m',
  BG_GREEN: '\x1b[42m',
  BG_YELLOW: '\x1b[43m',
  BG_BLUE: '\x1b[44m',
  BG_MAGENTA: '\x1b[45m',
  BG_CYAN: '\x1b[46m',

  // Combinations for specific use cases
  SUCCESS: '\x1b[32m\x1b[1m',      // Bright green
  ERROR: '\x1b[31m\x1b[1m',        // Bright red
  WARNING: '\x1b[33m\x1b[1m',      // Bright yellow
  INFO: '\x1b[36m',                // Cyan
  DEBUG: '\x1b[90m',               // Gray
  HIGHLIGHT: '\x1b[35m\x1b[1m',    // Bright magenta
  SCENE: '\x1b[34m\x1b[1m',        // Bright blue
  QUALITY: '\x1b[32m',             // Green
  TRANSLATION: '\x1b[37m\x1b[1m'   // Bright white
};

export class Claude4Translator {
  constructor(options = {}) {
    // Load and merge configuration
    const config = getValidatedConfig(process.env.NODE_ENV || 'production');
    this.config = { ...config, ...options };

    this.anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY,
    });

    this.model = this.config.model;
    this.maxTokens = this.config.maxTokens;
    this.temperature = this.config.temperature;

    // Initialize tools with configuration
    this.sceneDetector = new SceneDetector(this.config.sceneDetection);
    this.screenshotTool = new ScreenshotTool(this.config.screenshot);
    this.correctionTool = new CorrectionTool(this.config.correction);
    this.secondLanguageValidator = new SecondLanguageValidator(this.config.secondLanguageValidation || {});
    this.examplesReference = new ExamplesReference(this.config.examplesReference || {});
    this.metadataPersistence = new MetadataPersistence(this.config.metadataPersistence || {});
    this.contextManager = new ContextManager();

    // Translation settings
    this.maxRetries = this.config.maxRetries;
    this.enableScreenshots = this.config.enableScreenshots;
    this.enableCorrection = this.config.enableCorrection;

    console.log(`${COLORS.SUCCESS}🤖 [Claude4Translator] Initialized with model: ${this.model}${COLORS.RESET}`);

    // Setup graceful shutdown
    process.on('SIGINT', () => this.shutdown());
    process.on('SIGTERM', () => this.shutdown());
  }

  /**
   * Graceful shutdown - save metadata and cleanup
   */
  async shutdown() {
    console.log(`${COLORS.INFO}🔄 [Claude4Translator] Shutting down gracefully...${COLORS.RESET}`);

    try {
      await this.metadataPersistence.shutdown();
      console.log(`${COLORS.SUCCESS}✅ [Claude4Translator] Shutdown completed${COLORS.RESET}`);
    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [Claude4Translator] Error during shutdown: ${error.message}${COLORS.RESET}`);
    }

    process.exit(0);
  }

  /**
   * Main translation method that processes subtitle content with intelligent chunking
   * @param {string} subtitleContent - Raw subtitle content
   * @param {string|null} videoPath - Path to video file for screenshots (optional)
   * @param {Object} metadata - Anime metadata (title, characters, genres)
   * @param {string} fileName - Current file name for second language lookup
   * @returns {Promise<string>} - Translated content
   */
  async translateSubtitles(subtitleContent, videoPath = null, metadata = {}, fileName = '') {
    try {
      console.log(`${COLORS.INFO}🚀 [Claude4Translator] Starting translation process with Claude 3.5 Sonnet...${COLORS.RESET}`);

      // Store current file name for tools
      this.currentFileName = fileName;

      // Initialize context with metadata
      this.contextManager.initialize(metadata);

      // Load persistent metadata for this anime
      if (metadata.title) {
        const persistedMetadata = this.metadataPersistence.getAnimeMetadata(metadata.title, metadata);
        this.contextManager.initialize({ ...metadata, ...persistedMetadata });
      }

      // Detect scenes and create intelligent chunks
      const scenes = await this.sceneDetector.detectScenes(subtitleContent);
      console.log(`${COLORS.SCENE}📋 [Claude4Translator] Detected ${scenes.length} scenes${COLORS.RESET}`);

      let translatedContent = '';

      for (let i = 0; i < scenes.length; i++) {
        const scene = scenes[i];
        console.log(`${COLORS.SCENE}🎬 [Claude4Translator] Processing scene ${i + 1}/${scenes.length} (${scene.lines.length} lines)${COLORS.RESET}`);

        // Get visual context if enabled
        let visualContext = null;
        if (this.enableScreenshots && videoPath && scene.timestamp) {
          try {
            visualContext = await this.screenshotTool.captureFrame(videoPath, scene.timestamp);
            console.log(`${COLORS.INFO}📸 [Claude4Translator] Screenshot captured for scene ${i + 1}${COLORS.RESET}`);
          } catch (error) {
            console.warn(`${COLORS.WARNING}⚠️  [Claude4Translator] Screenshot failed for scene ${i + 1}: ${error.message}${COLORS.RESET}`);
          }
        }

        // Translate the scene
        const translatedScene = await this.translateScene(scene, visualContext, i);

        // Apply corrections and validation if enabled
        let finalScene = translatedScene;

        if (this.enableCorrection) {
          finalScene = await this.correctionTool.improveTranslation(
            scene.content,
            translatedScene,
            this.contextManager.getContext()
          );
        }

        // Note: Second Language Validator is now available as a tool during translation
        // The AI model will decide when to use it based on translation quality

        translatedContent += finalScene + '\n';

        // Update context with translated content
        this.contextManager.updateContext(scene, finalScene);
      }

      // Save metadata after translation
      if (metadata.title && metadata.episode) {
        this.metadataPersistence.addEpisodeData(metadata.title, metadata.episode, {
          scenes: scenes.length,
          lines: scenes.reduce((sum, scene) => sum + scene.lines.length, 0),
          quality: 0.8, // Default quality score
          characters: [...new Set(scenes.flatMap(scene => scene.speakers))],
          translatedAt: new Date().toISOString()
        });
      }

      console.log(`${COLORS.SUCCESS}✅ [Claude4Translator] Translation completed successfully!${COLORS.RESET}`);
      return translatedContent.trim();

    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [Claude4Translator] Translation failed: ${error.message}${COLORS.RESET}`);
      throw error;
    }
  }

  /**
   * Translate a single scene using Claude 4 with tool use
   * @param {Object} scene - Scene object with content and metadata
   * @param {Object|null} visualContext - Screenshot data if available
   * @param {number} sceneIndex - Index of current scene
   * @returns {Promise<string>} - Translated scene content
   */
  async translateScene(scene, visualContext, sceneIndex) {
    const tools = this.getTranslationTools();
    const systemPrompt = this.buildSystemPrompt();
    const userPrompt = this.buildUserPrompt(scene, visualContext, sceneIndex);

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(`${COLORS.INFO}🔄 [Claude4Translator] Requesting translation from Claude 3.5 Sonnet with tools... (attempt ${attempt}/${this.maxRetries})${COLORS.RESET}`);
        const response = await this.anthropic.messages.create({
          model: this.model,
          max_tokens: this.maxTokens,
          temperature: this.temperature,
          system: systemPrompt,
          messages: [
            {
              role: 'user',
              content: userPrompt
            }
          ],
          tools: tools,
          tool_choice: { type: "tool", name: "translate_with_context" }
        });

        // Process the response and handle tool calls
        return await this.processTranslationResponse(response, scene);

      } catch (error) {
        console.warn(`${COLORS.WARNING}⚠️  [Claude4Translator] Claude 3.5 Sonnet translation attempt ${attempt} failed: ${error.message}${COLORS.RESET}`);
        if (attempt === this.maxRetries) {
          console.error(`${COLORS.ERROR}❌ [Claude4Translator] All translation attempts failed, returning original content${COLORS.RESET}`);
          return scene.content; // Return original content as fallback
        }
        console.log(`${COLORS.DEBUG}⏳ [Claude4Translator] Retrying in ${2 * attempt} seconds...${COLORS.RESET}`);
        await this.sleep(2000 * attempt); // Exponential backoff
      }
    }

    // This should never be reached, but included for safety
    return scene.content;
  }

  /**
   * Define tools available for translation
   * @returns {Array} - Array of tool definitions
   */
  getTranslationTools() {
    return [
      {
        name: "analyze_scene_context",
        description: "Analyze the scene context to understand dialogue flow, character relationships, and emotional tone",
        input_schema: {
          type: "object",
          properties: {
            speakers: {
              type: "array",
              items: { type: "string" },
              description: "List of speakers in this scene"
            },
            scene_type: {
              type: "string",
              enum: ["dialogue", "monologue", "action", "comedy", "dramatic", "exposition"],
              description: "Type of scene being translated"
            },
            emotional_tone: {
              type: "string",
              description: "Overall emotional tone of the scene"
            },
            cultural_references: {
              type: "array",
              items: { type: "string" },
              description: "Any cultural references or idioms that need special handling"
            }
          },
          required: ["speakers", "scene_type", "emotional_tone"]
        }
      },
      {
        name: "translate_with_context",
        description: "Translate dialogue lines with full context awareness and Polish language optimization",
        input_schema: {
          type: "object",
          properties: {
            translations: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  original: { type: "string", description: "Original line" },
                  translation: { type: "string", description: "Polish translation" },
                  speaker: { type: "string", description: "Character speaking" },
                  notes: { type: "string", description: "Translation notes or reasoning" }
                },
                required: ["original", "translation", "speaker"]
              },
              description: "Array of translated dialogue lines"
            }
          },
          required: ["translations"]
        }
      },
      {
        name: "request_visual_context",
        description: "Request additional visual context when translation requires understanding of visual elements",
        input_schema: {
          type: "object",
          properties: {
            timestamp: { type: "string", description: "Timestamp for screenshot" },
            reason: { type: "string", description: "Why visual context is needed" }
          },
          required: ["timestamp", "reason"]
        }
      },
      {
        name: "lookup_translation_examples",
        description: "Look up relevant examples from the examples database for translation guidance",
        input_schema: {
          type: "object",
          properties: {
            query_text: { type: "string", description: "Text to find examples for" },
            context_type: {
              type: "string",
              enum: ["honorifics", "questions", "emotions", "actions", "dialogue", "formal", "informal"],
              description: "Type of context to search for"
            }
          },
          required: ["query_text"]
        }
      },
      {
        name: "validate_with_second_language",
        description: "Use second language source to validate and improve translation quality when the initial translation seems problematic or unclear",
        input_schema: {
          type: "object",
          properties: {
            original_text: { type: "string", description: "Original English text" },
            polish_translation: { type: "string", description: "Polish translation to validate" },
            reason: { type: "string", description: "Why second language validation is needed (e.g., 'unclear meaning', 'awkward phrasing', 'cultural reference')" }
          },
          required: ["original_text", "polish_translation", "reason"]
        }
      }
    ];
  }

  /**
   * Build system prompt for Claude 4
   * @returns {string} - System prompt
   */
  buildSystemPrompt() {
    return `You are an expert anime subtitle translator specializing in English to Polish translation. You have access to tools that help you understand scene context and produce high-quality translations.

CRITICAL: You must ALWAYS use the translate_with_context tool to provide the actual Polish translation. Do not provide translations in regular text - only use the tool.

Your workflow:
1. OPTIONAL: Use analyze_scene_context tool to understand the scene if needed
2. REQUIRED: Use translate_with_context tool to provide the Polish translation
3. OPTIONAL: Use validate_with_second_language tool ONLY if your initial translation has issues like:
   - Unclear or ambiguous meaning
   - Awkward phrasing that doesn't sound natural
   - Cultural references that need verification
   - Complex dialogue that might benefit from cross-validation
4. The translate_with_context tool output is the final translation - do not add explanatory text

Polish Language Guidelines:
- Use natural Polish sentence structure and word order
- Preserve Japanese honorifics (san, chan, kun, sama) without inflection
- Adapt idioms and cultural references for Polish audiences
- Use appropriate formality levels based on character relationships
- Avoid literal translations of phrasal verbs and expressions
- Ensure proper case declension and verb aspects

CRITICAL PUNCTUATION RULES (ALWAYS FOLLOW):
- ALWAYS add comma before: że, żeby, aby, bo, ale, jednak, więc, dlatego, ponieważ, chociaż
- NEVER add comma before: i, oraz, a (coordinating conjunctions)
- Examples: "Myślę, że..." "Chcę, żeby..." "Tak, ale..." "Poszedł i wrócił"

Quality Standards:
- Maintain the exact number of dialogue lines
- Preserve timing and speaker information
- Keep translations natural and engaging
- Ensure consistency in character speech patterns
- Adapt humor and wordplay when possible

Remember: Your final output must come from the translate_with_context tool, not from regular text responses.`;
  }

  /**
   * Build user prompt for specific scene
   * @param {Object} scene - Scene to translate
   * @param {Object|null} visualContext - Visual context if available
   * @param {number} sceneIndex - Scene index
   * @returns {string} - User prompt
   */
  buildUserPrompt(scene, visualContext, sceneIndex) {
    let prompt = `Please translate this anime scene (Scene ${sceneIndex + 1}) from English to Polish:

SCENE CONTENT:
${scene.content}

SCENE METADATA:
- Timestamp: ${scene.timestamp || 'Unknown'}
- Duration: ${scene.duration || 'Unknown'}
- Line count: ${scene.lines.length}

CONTEXT:
${this.contextManager.getContextSummary()}`;

    if (visualContext) {
      prompt += `\n\nVISUAL CONTEXT:
A screenshot has been captured at timestamp ${scene.timestamp} to provide visual context for this scene.`;
    }

    prompt += `\n\nIMPORTANT: You must use the tools in this specific order:
1. First use 'analyze_scene_context' to understand the scene
2. Then use 'translate_with_context' to provide the actual Polish translation
3. ONLY if your translation has quality issues, use 'validate_with_second_language' to improve it

Available tools:
- analyze_scene_context: Understand scene context and characters
- translate_with_context: Provide the main Polish translation (REQUIRED)
- validate_with_second_language: Use ONLY when translation quality is questionable
- lookup_translation_examples: Find relevant examples for difficult phrases
- request_visual_context: Request screenshots for visual context

Do not provide explanatory text - use the tools to deliver the translation directly.`;

    return prompt;
  }

  /**
   * Process Claude's response and handle tool calls
   * @param {Object} response - Claude's response
   * @param {Object} scene - Original scene
   * @returns {Promise<string>} - Final translated content
   */
  async processTranslationResponse(response, scene) {
    console.log(`${COLORS.DEBUG}🔍 [Claude4Translator] Processing translation response...${COLORS.RESET}`);
    console.log(`${COLORS.DEBUG}📝 [Claude4Translator] Response content types: ${response.content.map((c) => c.type).join(', ')}${COLORS.RESET}`);

    let finalTranslation = '';

    for (const content of response.content) {
      if (content.type === 'text') {
        console.log(`${COLORS.DEBUG}📄 [Claude4Translator] AI Text Response:${COLORS.RESET}`);
        console.log(`${COLORS.TRANSLATION}${content.text}${COLORS.RESET}`);
        finalTranslation += content.text;
      } else if (content.type === 'tool_use') {
        console.log(`${COLORS.HIGHLIGHT}🔧 [Claude4Translator] AI Tool Use: ${content.name}${COLORS.RESET}`);
        const toolResult = await this.handleToolCall(content, scene);
        if (toolResult && typeof toolResult === 'string') {
          console.log(`${COLORS.TRANSLATION}✨ [Claude4Translator] AI Translation Output:${COLORS.RESET}`);
          console.log(`${COLORS.TRANSLATION}${toolResult}${COLORS.RESET}`);
          finalTranslation += toolResult;
        }
      }
    }

    console.log(`${COLORS.SUCCESS}📊 [Claude4Translator] Final translation length: ${finalTranslation.length} characters${COLORS.RESET}`);
    return finalTranslation || scene.content; // Fallback to original if translation fails
  }

  /**
   * Handle individual tool calls
   * @param {Object} toolCall - Tool call from Claude
   * @param {Object} _scene - Current scene (unused but kept for future use)
   * @returns {Promise<string|null>} - Tool result
   */
  async handleToolCall(toolCall, _scene) {
    const { name, input } = toolCall;

    try {
      switch (name) {
        case 'analyze_scene_context':
          this.contextManager.updateSceneAnalysis(input);
          return null; // Analysis doesn't return translation content

        case 'translate_with_context':
          console.log(`${COLORS.DEBUG}🔍 [Claude4Translator] Processing Claude 3.5 Sonnet translations input:${COLORS.RESET}`);
          console.log(`${COLORS.DEBUG}📊 [Claude4Translator] Input type: ${typeof input.translations}, Is Array: ${Array.isArray(input.translations)}${COLORS.RESET}`);
          console.log(`${COLORS.DEBUG}📋 [Claude4Translator] Input content: ${JSON.stringify(input.translations, null, 2)}${COLORS.RESET}`);
          return this.formatTranslations(input.translations);

        case 'request_visual_context':
          // This would trigger additional screenshot capture if needed
          console.log(`[Claude4Translator] Visual context requested: ${input.reason}`);
          return null;

        case 'lookup_translation_examples':
          try {
            const examplesResult = await this.examplesReference.findRelevantExamples(
              input.query_text,
              '',
              { contextType: input.context_type }
            );

            if (examplesResult.hasExamples) {
              const examplesText = examplesResult.relevantExamples
                .slice(0, 3)
                .map(ex => `English: ${ex.englishSource}\nPolish: ${ex.idealOutput}`)
                .join('\n\n');

              console.log(`${COLORS.INFO}📚 [Claude4Translator] Found ${examplesResult.relevantExamples.length} relevant examples${COLORS.RESET}`);
              return `Relevant examples found:\n\n${examplesText}`;
            } else {
              return "No relevant examples found in the database.";
            }
          } catch (error) {
            console.warn(`${COLORS.WARNING}⚠️  [Claude4Translator] Examples lookup failed: ${error.message}${COLORS.RESET}`);
            return "Examples lookup failed.";
          }

        case 'validate_with_second_language':
          try {
            console.log(`${COLORS.INFO}🔍 [Claude4Translator] AI requested second language validation: ${input.reason}${COLORS.RESET}`);

            if (!this.config.secondLanguageValidation?.enableValidation) {
              console.log(`${COLORS.WARNING}⚠️  [Claude4Translator] Second language validation is disabled in config${COLORS.RESET}`);
              return "Second language validation is not available.";
            }

            const validationResult = await this.secondLanguageValidator.validateTranslation(
              input.original_text,
              input.polish_translation,
              {
                fileName: this.currentFileName,
                animeTitle: this.contextManager.animeMetadata?.title,
                episode: this.contextManager.animeMetadata?.episode,
                sceneType: 'dialogue'
              }
            );

            if (validationResult.improvedTranslation) {
              console.log(`${COLORS.SUCCESS}✅ [Claude4Translator] Second language validation provided improved translation${COLORS.RESET}`);
              return `Validation complete. Improved translation: ${validationResult.improvedTranslation}`;
            } else if (validationResult.isValid) {
              console.log(`${COLORS.SUCCESS}✅ [Claude4Translator] Second language validation confirmed translation quality${COLORS.RESET}`);
              return "Validation complete. Original translation is acceptable.";
            } else {
              console.log(`${COLORS.WARNING}⚠️  [Claude4Translator] Second language validation found issues but no improvement available${COLORS.RESET}`);
              return `Validation found issues: ${validationResult.issues?.join(', ') || 'Quality concerns detected'}`;
            }
          } catch (error) {
            console.warn(`${COLORS.WARNING}⚠️  [Claude4Translator] Second language validation failed: ${error.message}${COLORS.RESET}`);
            return "Second language validation failed.";
          }

        default:
          console.warn(`[Claude4Translator] Unknown tool: ${name}`);
          return null;
      }
    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [Claude4Translator] Tool call failed: ${error.message}${COLORS.RESET}`);
      console.error(`${COLORS.DEBUG}🔍 [Claude4Translator] Tool: ${name}, Input: ${JSON.stringify(input, null, 2)}${COLORS.RESET}`);
      return null;
    }
  }

  /**
   * Format translations into final output
   * @param {Array|any} translations - Array of translation objects or other format
   * @returns {string} - Formatted translation content
   */
  formatTranslations(translations) {
    console.log(`${COLORS.DEBUG}🔧 [Claude4Translator] Formatting translations...${COLORS.RESET}`);

    // Handle case where translations is not an array
    if (!Array.isArray(translations)) {
      console.log(`${COLORS.INFO}📝 [Claude4Translator] Input is ${typeof translations}, attempting to convert to array format${COLORS.RESET}`);

      // If it's a string, try to parse it as JSON first
      if (typeof translations === 'string') {
        console.log(`${COLORS.DEBUG}🔍 [Claude4Translator] Attempting to parse string as JSON...${COLORS.RESET}`);
        try {
          const parsed = JSON.parse(translations);
          if (Array.isArray(parsed)) {
            console.log(`${COLORS.SUCCESS}✅ [Claude4Translator] Successfully parsed JSON array with ${parsed.length} items${COLORS.RESET}`);
            // Recursively call formatTranslations with the parsed array
            return this.formatTranslations(parsed);
          } else if (parsed && typeof parsed === 'object') {
            console.log(`${COLORS.INFO}📝 [Claude4Translator] Parsed JSON object, checking for translation properties${COLORS.RESET}`);
            // If it's an object with translation properties, convert to array
            if (parsed.translation && parsed.speaker) {
              console.log(`${COLORS.SUCCESS}✅ [Claude4Translator] Converting single object to array format${COLORS.RESET}`);
              return this.formatTranslations([parsed]);
            }
          }
        } catch (error) {
          console.log(`${COLORS.DEBUG}🔍 [Claude4Translator] String is not valid JSON, treating as plain text${COLORS.RESET}`);
        }

        console.log(`${COLORS.INFO}📝 [Claude4Translator] Returning string translation directly (no format conversion needed)${COLORS.RESET}`);
        return translations;
      }

      // If it's an object with a single translation, try to extract it
      if (typeof translations === 'object' && translations !== null) {
        // Check if it has translation properties
        if (translations.translation && translations.speaker) {
          console.log(`${COLORS.SUCCESS}✅ [Claude4Translator] Converting single object to formatted string${COLORS.RESET}`);
          return `${translations.speaker}: ${translations.translation}`;
        }

        // Check if it's an object with an array property
        if (translations.translations && Array.isArray(translations.translations)) {
          console.log(`${COLORS.SUCCESS}✅ [Claude4Translator] Found nested translations array${COLORS.RESET}`);
          return this.formatTranslations(translations.translations);
        }

        // Try to convert object to string
        console.log(`${COLORS.INFO}📝 [Claude4Translator] Converting object to JSON string${COLORS.RESET}`);
        return JSON.stringify(translations, null, 2);
      }

      // Fallback: convert to string
      console.log(`${COLORS.INFO}📝 [Claude4Translator] Converting to string as fallback${COLORS.RESET}`);
      return String(translations);
    }

    // Normal array processing
    console.log(`${COLORS.SUCCESS}✅ [Claude4Translator] Processing ${translations.length} translation objects${COLORS.RESET}`);
    return translations
      .map(t => {
        if (t && t.speaker && t.translation) {
          return `${t.speaker}: ${t.translation}`;
        } else if (t && typeof t === 'object') {
          // Try to extract translation from object with different property names
          const speaker = t.speaker || t.character || t.actor || 'Unknown';
          const translation = t.translation || t.polish || t.text || String(t);
          console.log(`${COLORS.DEBUG}🔍 [Claude4Translator] Extracted speaker: "${speaker}", translation: "${translation.substring(0, 50)}..."${COLORS.RESET}`);
          return `${speaker}: ${translation}`;
        } else {
          console.log(`${COLORS.DEBUG}🔍 [Claude4Translator] Converting non-object item to string: ${JSON.stringify(t)}${COLORS.RESET}`);
          return String(t);
        }
      })
      .join('\n');
  }

  /**
   * Sleep utility
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
